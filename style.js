document.addEventListener("DOMContentLoaded", function () {
  // Slider functionality
  const slides = document.querySelectorAll(".slide");
  const navDots = document.querySelectorAll(".nav-dot");
  let currentSlide = 0;
  let slideInterval;
  const slideIntervalTime = 5000; // 5 seconds

  // Initialize slider only if elements exist
  if (slides.length > 0 && navDots.length > 0) {
    // Set background images
    slides.forEach((slide) => {
      const bgImage = slide.getAttribute("data-bg");
      if (bgImage) {
        slide.style.backgroundImage = `url('${bgImage}')`;
      }
    });

    // Function to show specific slide
    function showSlide(index) {
      if (index < 0 || index >= slides.length) return;

      // Remove active class from all slides and dots
      slides.forEach((slide) => slide.classList.remove("active"));
      navDots.forEach((dot) => dot.classList.remove("active"));

      // Add active class to current slide and dot
      slides[index].classList.add("active");
      if (navDots[index]) {
        navDots[index].classList.add("active");
      }

      currentSlide = index;
    }

    // Function to go to next slide
    function nextSlide() {
      const next = (currentSlide + 1) % slides.length;
      showSlide(next);
    }

    // Function to start auto-advance
    function startSlideInterval() {
      slideInterval = setInterval(nextSlide, slideIntervalTime);
    }

    // Function to stop auto-advance
    function stopSlideInterval() {
      if (slideInterval) {
        clearInterval(slideInterval);
      }
    }

    // Add click event listeners to navigation dots
    navDots.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        stopSlideInterval();
        showSlide(index);
        startSlideInterval();
      });
    });

    // Pause on hover
    const sliderContainer = document.querySelector(".hero-slider");
    if (sliderContainer) {
      sliderContainer.addEventListener("mouseenter", stopSlideInterval);
      sliderContainer.addEventListener("mouseleave", startSlideInterval);
    }

    // Initialize first slide and start auto-advance
    showSlide(0);
    startSlideInterval();
  }

  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll(".menu__link");
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      const href = this.getAttribute("href");
      if (href && href.startsWith("#")) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) {
          target.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    });
  });

  // Newsletter form handling
  const newsletterForm = document.querySelector(".newsletter-form");
  if (newsletterForm) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();
      const emailInput = this.querySelector(".newsletter-input");
      const email = emailInput.value.trim();

      if (email && isValidEmail(email)) {
        // Here you would typically send the email to your server
        alert("Thank you for subscribing to our newsletter!");
        emailInput.value = "";
      } else {
        alert("Please enter a valid email address.");
      }
    });
  }

  // Email validation function
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  // Get all accordion and panel elements
  let accordions = document.getElementsByClassName('accordion');
  let panels = document.getElementsByClassName('panel');

  // Function to close all accordions
  function closeAllAccordions() {
    for (let i = 0; i < accordions.length; i++) {
      accordions[i].classList.remove('active');
      accordions[i].innerHTML = '+';
      panels[i].classList.remove('open');
    }
  }

  // Add event listener to each accordion
  for (let i = 0; i < accordions.length; i++) {
    accordions[i].addEventListener('click', function() {
      // Check if this accordion is currently active
      const isActive = this.classList.contains('active');

      // Close all accordions first
      closeAllAccordions();

      // If this accordion wasn't active, open it
      if (!isActive) {
        this.classList.add('active');
        this.innerHTML = '-';
        panels[i].classList.add('open');
      }
    });
  }



});
