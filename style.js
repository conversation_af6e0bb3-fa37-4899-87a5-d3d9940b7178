// ساده‌ترین کد JavaScript برای مبتدیان

// وقتی صفحه بارگذاری شد، این کد اجرا می‌شود
document.addEventListener("DOMContentLoaded", function () {

  // ========== اسلایدر ساده ==========

  // پیدا کردن تمام اسلایدها و دکمه‌های ناوبری
  var slides = document.querySelectorAll(".slide");
  var dots = document.querySelectorAll(".nav-dot");
  var currentSlide = 0; // اسلاید فعلی

  // اگر اسلایدها وجود دارند
  if (slides.length > 0) {

    // تنظیم تصویر پس‌زمینه برای هر اسلاید
    for (var i = 0; i < slides.length; i++) {
      var bgImage = slides[i].getAttribute("data-bg");
      if (bgImage) {
        slides[i].style.backgroundImage = "url('" + bgImage + "')";
      }
    }

    // تابع برای نمایش اسلاید مشخص
    function showSlide(slideNumber) {
      // حذف کلاس active از همه اسلایدها
      for (var i = 0; i < slides.length; i++) {
        slides[i].classList.remove("active");
      }

      // حذف کلاس active از همه دکمه‌ها
      for (var i = 0; i < dots.length; i++) {
        dots[i].classList.remove("active");
      }

      // اضافه کردن کلاس active به اسلاید و دکمه مورد نظر
      slides[slideNumber].classList.add("active");
      dots[slideNumber].classList.add("active");

      currentSlide = slideNumber;
    }

    // تابع برای رفتن به اسلاید بعدی
    function nextSlide() {
      var next = currentSlide + 1;
      if (next >= slides.length) {
        next = 0; // برگشت به اولین اسلاید
      }
      showSlide(next);
    }

    // اضافه کردن کلیک به دکمه‌های ناوبری
    for (var i = 0; i < dots.length; i++) {
      dots[i].onclick = function() {
        var slideIndex = this.getAttribute("data-slide");
        showSlide(parseInt(slideIndex));
      };
    }

    // تغییر خودکار اسلاید هر 5 ثانیه
    setInterval(nextSlide, 5000);

    // نمایش اولین اسلاید
    showSlide(0);
  }

  // ========== آکاردئون ساده ==========

  // پیدا کردن تمام آکاردئون‌ها
  var accordions = document.querySelectorAll('.accordion');
  var panels = document.querySelectorAll('.panel');

  // اضافه کردن کلیک به هر آکاردئون
  for (var i = 0; i < accordions.length; i++) {
    accordions[i].onclick = function() {

      // بستن همه آکاردئون‌ها
      for (var j = 0; j < accordions.length; j++) {
        accordions[j].classList.remove('active');
        accordions[j].innerHTML = '+';
        panels[j].classList.remove('open');
      }

      // پیدا کردن شماره آکاردئون کلیک شده
      var clickedIndex = -1;
      for (var k = 0; k < accordions.length; k++) {
        if (accordions[k] === this) {
          clickedIndex = k;
          break;
        }
      }

      // باز کردن آکاردئون کلیک شده
      if (clickedIndex !== -1) {
        this.classList.add('active');
        this.innerHTML = '-';
        panels[clickedIndex].classList.add('open');
      }
    };
  }

  // ========== فرم خبرنامه ساده ==========

  // پیدا کردن فرم خبرنامه
  var newsletterForm = document.querySelector(".newsletter-form");

  if (newsletterForm) {
    newsletterForm.onsubmit = function(e) {
      e.preventDefault(); // جلوگیری از ارسال واقعی فرم

      var emailInput = this.querySelector(".newsletter-input");
      var email = emailInput.value;

      // بررسی ساده ایمیل
      if (email && email.includes("@") && email.includes(".")) {
        alert("متشکرم! شما در خبرنامه ما عضو شدید.");
        emailInput.value = ""; // پاک کردن فیلد
      } else {
        alert("لطفاً یک ایمیل معتبر وارد کنید.");
      }
    };
  }

});
